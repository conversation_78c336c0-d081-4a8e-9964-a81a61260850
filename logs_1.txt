(base) PS C:\Users\<USER>\Nihilent_X_Snowflake_1> python scripts/deploy_to_snowflake.py --config config/snowflake_config.json --verbose
2025-05-29 03:15:06,736 - INFO - Starting SalesGenie AI deployment...
2025-05-29 03:15:06,736 - INFO - Connecting to Snowflake...
2025-05-29 03:15:06,736 - INFO - Snowflake Connector for Python Version: 3.15.0, Python Version: 3.9.12, Platform: Windows-10-10.0.26100-SP0
2025-05-29 03:15:06,736 - DEBUG - connect
2025-05-29 03:15:06,736 - DEBUG - __config
2025-05-29 03:15:06,736 - INFO - Connecting to GLOBAL Snowflake domain
2025-05-29 03:15:06,736 - DEBUG - This connection is in OCSP Fail Open Mode. TLS Certificates would be checked for validity and revocation status. Any other Certificate Revocation related exceptions or OCSP Responder failures would be disregarded in favor of connectivity.
2025-05-29 03:15:06,736 - DEBUG - use_numpy: False
2025-05-29 03:15:06,736 - DEBUG - initialized
2025-05-29 03:15:06,736 - DEBUG - REST API object was created: your_account.snowflakecomputing.com:443
2025-05-29 03:15:06,736 - DEBUG - authenticate
2025-05-29 03:15:06,736 - DEBUG - account=your_account, user=your_user, database=salesgenie_ai, schema=crm_data, warehouse=salesgenie_ai_wh, role=salesgenie_ai_role, request_id=cffe648a-473e-487a-91da-51feefb47284
2025-05-29 03:15:06,736 - DEBUG - body['data']: {'CLIENT_APP_ID': 'PythonConnector', 'CLIENT_APP_VERSION': '3.15.0', 'SVN_REVISION': None, 'ACCOUNT_NAME': 'your_account', 'LOGIN_NAME': 'your_user', 'CLIENT_ENVIRONMENT': {'APPLICATION': 'PythonConnector', 'OS': 'Windows', 'OS_VERSION': 'Windows-10-10.0.26100-SP0', 'PYTHON_VERSION': '3.9.12', 'PYTHON_RUNTIME': 'CPython', 'PYTHON_COMPILER': 'MSC v.1916 64 bit (AMD64)', 'OCSP_MODE': 'FAIL_OPEN', 'TRACING': 10, 'LOGIN_TIMEOUT': None, 'NETWORK_TIMEOUT': None, 'SOCKET_TIMEOUT': None}, 'PASSWORD': '******', 'SESSION_PARAMETERS': {'CLIENT_PREFETCH_THREADS': 4}}
2025-05-29 03:15:06,736 - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-05-29 03:15:06,736 - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-05-29 03:15:06,736 - DEBUG - Session status for SessionPool 'your_account.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:15:06,736 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:15:06,736 - DEBUG - Request guid: be20ae71-0d19-4e93-9851-974359ae1066
2025-05-29 03:15:06,736 - DEBUG - socket timeout: 60
2025-05-29 03:15:06,747 - DEBUG - Starting new HTTPS connection (1): your_account.snowflakecomputing.com:443
2025-05-29 03:15:08,066 - DEBUG - OCSP Mode: FAIL_OPEN, OCSP response cache file name: None
2025-05-29 03:15:08,125 - DEBUG - cache directory: C:\Users\<USER>\AppData\Local\Snowflake\Caches
2025-05-29 03:15:08,127 - DEBUG - ocsp_response_cache_uri: file://C:/Users/<USER>/AppData/Local/Snowflake/Caches/ocsp_response_cache.json
2025-05-29 03:15:08,127 - DEBUG - OCSP_VALIDATION_CACHE size: 0
2025-05-29 03:15:08,127 - DEBUG - OCSP response cache server is enabled: http://ocsp.snowflakecomputing.com/ocsp_response_cache.json
2025-05-29 03:15:08,127 - DEBUG - OCSP dynamic cache server RETRY URL: None
2025-05-29 03:15:08,128 - DEBUG - Failed to check OCSP response cache file. No worry. It will validate with OCSP server: file: C:/Users/<USER>/AppData/Local/Snowflake/Caches/ocsp_response_cache.json, lock directory: C:/Users/<USER>/AppData/Local/Snowflake/Caches/ocsp_response_cache.json.lck, error: [WinError 2] The system cannot find the file specified: 'C:/Users/<USER>/AppData/Local/Snowflake/Caches/ocsp_response_cache.json'     
2025-05-29 03:15:08,128 - DEBUG - Failed to locate OCSP response cache file. No worry. It will validate with OCSP server: C:/Users/<USER>/AppData/Local/Snowflake/Caches/ocsp_response_cache.json
2025-05-29 03:15:08,128 - DEBUG - validating certificate: your_account.snowflakecomputing.com 
2025-05-29 03:15:08,128 - DEBUG - # of certificates: 4
2025-05-29 03:15:08,129 - DEBUG - reading certificate bundle: C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\certifi\cacert.pem
2025-05-29 03:15:08,143 - DEBUG - subject: OrderedDict([('common_name', '*.prod3.us-west-2.snowflakecomputing.com')]), issuer: OrderedDict([('country_name', 'US'), ('organization_name', 'Amazon'), ('common_name', 'Amazon RSA 2048 M03')])
2025-05-29 03:15:08,143 - DEBUG - subject: OrderedDict([('country_name', 'US'), ('organization_name', 'Amazon'), ('common_name', 'Amazon RSA 2048 M03')]), issuer: OrderedDict([('country_name', 'US'), ('organization_name', 'Amazon'), ('common_name', 'Amazon Root CA 1')])
2025-05-29 03:15:08,143 - DEBUG - A trusted root certificate found: OrderedDict([('country_name', 'US'), ('organization_name', 'Amazon'), ('common_name', 'Amazon RSA 2048 M03')]), stopping chain traversal here
2025-05-29 03:15:08,149 - DEBUG - not found issuer_der: OrderedDict([('country_name', 'US'), ('organization_name', 'Amazon'), ('common_name', 'Amazon Root CA 1')])
2025-05-29 03:15:08,151 - DEBUG - cache miss for subject: 'OrderedDict([('common_name', '*.prod3.us-west-2.snowflakecomputing.com')])'
2025-05-29 03:15:08,151 - DEBUG - started downloading OCSP response cache file: http://ocsp.snowflakecomputing.com/ocsp_response_cache.json
2025-05-29 03:15:08,154 - DEBUG - Starting new HTTP connection (1): ocsp.snowflakecomputing.com:80
2025-05-29 03:15:08,807 - DEBUG - http://ocsp.snowflakecomputing.com:80 "GET /ocsp_response_cache.json HTTP/1.1" 200 586580
2025-05-29 03:15:09,080 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2026-07-22 00:00:00+00:00
2025-05-29 03:15:09,083 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2026-07-22 00:00:00+00:00
2025-05-29 03:15:09,093 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,098 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,100 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,102 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,106 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,109 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,111 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,114 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,116 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,120 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,123 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,126 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,128 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,130 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,132 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,133 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,134 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,137 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,141 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,144 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,147 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,148 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,150 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,152 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,157 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,159 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,162 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,162 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,172 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,175 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:15+00:00
2025-05-29 03:15:09,177 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,180 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,182 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,184 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,186 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-23 15:50:25+00:00
2025-05-29 03:15:09,189 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,193 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,195 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,197 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,199 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,200 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,201 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,204 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,207 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,210 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,212 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,215 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,216 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,218 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,220 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,260 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,278 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,300 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,302 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,305 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,309 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,326 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,329 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,331 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,332 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,334 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,335 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,336 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,339 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,341 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,344 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,346 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,347 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,348 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,349 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,352 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,353 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,356 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,358 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,360 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,361 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,362 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,364 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,365 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,367 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,368 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,370 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,372 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,374 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,375 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,376 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,376 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,376 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,381 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,383 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,384 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,385 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,387 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,390 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,392 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,393 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,393 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,393 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,393 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,399 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,400 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,402 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,405 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,407 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,409 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,412 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,413 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,414 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,416 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,417 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,418 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,420 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,425 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,427 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,429 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,430 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,431 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,432 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,434 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,436 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,439 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,441 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,443 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:15+00:00
2025-05-29 03:15:09,444 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,446 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,446 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,450 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:15+00:00
2025-05-29 03:15:09,452 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,455 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,458 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,460 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,462 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,463 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:15+00:00
2025-05-29 03:15:09,464 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,467 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,468 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,470 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:15+00:00
2025-05-29 03:15:09,473 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,475 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,477 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:15+00:00
2025-05-29 03:15:09,478 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,480 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,482 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,484 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,486 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,490 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,493 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,496 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:14+00:00
2025-05-29 03:15:09,498 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,500 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2026-07-22 00:00:00+00:00
2025-05-29 03:15:09,526 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2026-07-22 00:00:00+00:00
2025-05-29 03:15:09,552 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2025-06-26 15:50:13+00:00
2025-05-29 03:15:09,553 - DEBUG - ended downloading OCSP response cache file. elapsed time: 1.4019672870635986s
2025-05-29 03:15:09,554 - DEBUG - downloaded OCSP response cache file from http://ocsp.snowflakecomputing.com/ocsp_response_cache.json
2025-05-29 03:15:09,554 - DEBUG - # of certificates: 404
2025-05-29 03:15:09,554 - DEBUG - hit cache for subject: OrderedDict([('common_name', '*.prod3.us-west-2.snowflakecomputing.com')])
2025-05-29 03:15:09,554 - DEBUG - using OCSP response cache
2025-05-29 03:15:09,554 - DEBUG - Certificate is NOT attached in Basic OCSP Response. Using issuer's certificate
2025-05-29 03:15:09,554 - DEBUG - Verifying the OCSP response is signed by the issuer.        
2025-05-29 03:15:09,561 - DEBUG - Verifying the attached certificate is signed by the issuer. Valid Not After: 2026-07-22 00:00:00+00:00
2025-05-29 03:15:09,561 - DEBUG - hit cache for subject: OrderedDict([('country_name', 'US'), ('organization_name', 'Amazon'), ('common_name', 'Amazon RSA 2048 M03')])
2025-05-29 03:15:09,564 - DEBUG - using OCSP response cache
2025-05-29 03:15:09,565 - DEBUG - Certificate is attached in Basic OCSP Response
2025-05-29 03:15:09,565 - DEBUG - Verifying the attached certificate is signed by the issuer  
2025-05-29 03:15:09,565 - DEBUG - Valid Not After: 2026-07-22 00:00:00+00:00
2025-05-29 03:15:09,566 - DEBUG - Verifying the OCSP response is signed by the issuer.        
2025-05-29 03:15:09,567 - DEBUG - Attempting to acquire lock ************* on C:\Users\<USER>\AppData\Local\Snowflake\Caches\ocsp_response_validation_cache.json.lock
2025-05-29 03:15:09,569 - DEBUG - Lock ************* acquired on C:\Users\<USER>\AppData\Local\Snowflake\Caches\ocsp_response_validation_cache.json.lock
2025-05-29 03:15:09,578 - DEBUG - Attempting to release lock ************* on C:\Users\<USER>\AppData\Local\Snowflake\Caches\ocsp_response_validation_cache.json.lock
2025-05-29 03:15:09,586 - DEBUG - Lock ************* released on C:\Users\<USER>\AppData\Local\Snowflake\Caches\ocsp_response_validation_cache.json.lock
2025-05-29 03:15:09,587 - DEBUG - writing OCSP response cache file to C:/Users/<USER>/AppData/Local/Snowflake/Caches/ocsp_response_cache.json
2025-05-29 03:15:09,587 - DEBUG - encoding OCSP response cache to JSON
2025-05-29 03:15:09,638 - DEBUG - ok
2025-05-29 03:15:10,214 - DEBUG - https://your_account.snowflakecomputing.com:443 "POST /session/v1/login-request?request_id=cffe648a-473e-487a-91da-51feefb47284&databaseName=salesgenie_ai&schemaName=crm_data&warehouse=salesgenie_ai_wh&roleName=salesgenie_ai_role&request_guid=be20ae71-0d19-4e93-9851-974359ae1066 HTTP/1.1" 404 14838
2025-05-29 03:15:10,224 - DEBUG - Session status for SessionPool 'your_account.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:15:10,224 - ERROR - Failed to connect to Snowflake: 250003 (08001): None: 404 Not Found: post https://your_account.snowflakecomputing.com:443/session/v1/login-request?request_id=cffe648a-473e-487a-91da-51feefb47284&databaseName=salesgenie_ai&schemaName=crm_data&warehouse=salesgenie_ai_wh&roleName=salesgenie_ai_role&request_guid=be20ae71-0d19-4e93-9851-974359ae1066

============================================================
❌ DEPLOYMENT FAILED
============================================================

Please check the logs above for error details.
Common issues:
- Incorrect Snowflake credentials
- Insufficient privileges
- Network connectivity issues